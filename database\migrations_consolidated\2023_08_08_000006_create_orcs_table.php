<?php

use App\Models\Orc;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orcs', function (Blueprint $table) {
            $table->id();
            // Note: Original had bair_id, but this was replaced with korpus_id in 2025_01_14
            $table->unsignedBigInteger('korpus_id'); // Replaced bair_id
            $table->string('number');
            $table->integer('begin_toot_number')->nullable(); // Added from 2024_12_03
            $table->integer('end_toot_number')->nullable(); // Added from 2024_12_03
            $table->string('code', 30)->nullable(); // Added from 2025_01_27 (CVSecurity integration)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orcs');
    }
};
