<?php

use App\Models\OrshinSuugch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orshin_suugches', function (Blueprint $table) {
            $table->id();
            $table->string(OrshinSuugch::LAST_NAME)->nullable();
            $table->text(OrshinSuugch::NAME)->nullable(); // Made nullable and text type
            $table->string(OrshinSuugch::PHONE)->unique();
            $table->string(OrshinSuugch::EMAIL)->unique()->nullable();
            $table->boolean(OrshinSuugch::IS_ADMIN)->default(false);
            $table->unsignedBigInteger(OrshinSuugch::PARENT_ID)->nullable();
            $table->string('device_code')->nullable(); // Added from 2023_12_20
            $table->string('uniq_code')->unique()->nullable(); // Added from 2024_12_31 and modified in 2025_01_03
            $table->string(OrshinSuugch::CODE, 30)->nullable(); // Added from 2025_01_27 (CVSecurity integration)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orshin_suugches');
    }
};
