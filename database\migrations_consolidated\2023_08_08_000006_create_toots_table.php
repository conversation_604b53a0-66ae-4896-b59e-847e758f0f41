<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('toots', function (Blueprint $table) {
            $table->id();
            // Note: Original had bair_id and orc_id, but these were replaced with korpus_id in 2025_01_16
            $table->unsignedBigInteger('korpus_id')->nullable(); // Replaced bair_id and orc_id
            $table->bigInteger('number');
            $table->foreignId('davhar_id')->nullable()->constrained('davhars')->cascadeOnDelete(); // Added from 2025_01_28
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('toots');
    }
};
