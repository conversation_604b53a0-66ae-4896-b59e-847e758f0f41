<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orcs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('korpus_id')->constrained('korpuses')->cascadeOnDelete();
            $table->string('number');
            $table->integer('begin_toot_number')->nullable();
            $table->integer('end_toot_number')->nullable();
            $table->string('code', 30)->nullable(); // CVSecurity integration code
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orcs');
    }
};
