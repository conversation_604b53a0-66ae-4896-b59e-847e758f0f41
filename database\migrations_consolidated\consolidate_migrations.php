<?php

/**
 * Migration Consolidation Script
 * 
 * This script helps automate the process of replacing original migrations
 * with consolidated versions for a fresh Laravel project.
 * 
 * Usage: php consolidate_migrations.php
 */

class MigrationConsolidator
{
    private $originalMigrationsPath;
    private $consolidatedMigrationsPath;
    private $backupPath;
    
    public function __construct()
    {
        $this->originalMigrationsPath = __DIR__ . '/../migrations';
        $this->consolidatedMigrationsPath = __DIR__;
        $this->backupPath = __DIR__ . '/../migrations_backup';
    }
    
    public function consolidate()
    {
        echo "🚀 Starting Migration Consolidation Process...\n\n";
        
        // Step 1: Create backup
        $this->createBackup();
        
        // Step 2: Remove consolidated migrations from original directory
        $this->removeConsolidatedMigrations();
        
        // Step 3: Copy consolidated migrations to original directory
        $this->copyConsolidatedMigrations();
        
        // Step 4: Verify consolidation
        $this->verifyConsolidation();
        
        echo "✅ Migration consolidation completed successfully!\n";
        echo "📁 Original migrations backed up to: {$this->backupPath}\n";
        echo "🔄 Run 'php artisan migrate:fresh' to apply consolidated migrations\n\n";
    }
    
    private function createBackup()
    {
        echo "📦 Creating backup of original migrations...\n";
        
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
        
        $files = glob($this->originalMigrationsPath . '/*.php');
        foreach ($files as $file) {
            $filename = basename($file);
            copy($file, $this->backupPath . '/' . $filename);
        }
        
        echo "✅ Backup created with " . count($files) . " files\n\n";
    }
    
    private function removeConsolidatedMigrations()
    {
        echo "🗑️  Removing original migrations that have been consolidated...\n";
        
        $migrationsToRemove = [
            // orshin_suugches related
            '2023_12_05_082329_alter_orshin_suugches_table_make_name_nullable.php',
            '2023_12_20_082329_add_column_orshin_suugches_table.php',
            '2024_12_31_021558_alter_orshin_suugches_table.php',
            '2025_01_03_021558_alter_orshin_suugches_table.php',
            '2025_01_27_130000_add_code_to_orshin_suugches_table.php',
            
            // korpuses related
            '2025_01_16_035737_alter_korpuses_table.php',
            '2025_01_17_035738_alter_korpuses_table.php',
            '2025_05_27_062043_add_code_to_korpuses_table.php',
            
            // sukhs related
            '2025_05_27_062042_add_code_to_sukhs_table.php',
            
            // toots related
            '2025_01_16_035738_alter_toots_table.php',
            '2025_01_28_000002_add_davhar_id_to_toots_table.php',
            
            // packages related
            '2023_12_01_000001_remove_column_packages_table.php',
            '2023_12_01_000002_add_column_packages_table.php',
            '2023_12_11_082329_alter_packages_add_valid_day_column.php',
            
            // orshin_suugch_toots related
            '2024_12_05_021558_alter_orshin_suugch_toots_table.php',
            '2025_01_14_035736_alter_orshin_suugch_toots_table.php',
            
            // orcs related
            '2024_12_03_082941_alter_orcs_table.php',
            '2025_01_14_035735_alter_orcs_table.php',
            '2025_01_27_120000_add_code_to_orcs_table.php',
            
            // nehemjlehs related
            '2025_01_08_021558_alter_nehemjlehs_table.php',
            '2025_01_10_021558_alter_nehemjlehs_table.php',
            '2025_01_10_021559_alter_nehemjlehs_table.php',
            '2025_01_13_021559_alter_nehemjlehs_table.php',
            '2025_01_14_021559_alter_nehemjlehs_table.php',
            '2025_01_17_035741_alter_nehemjlehs_table.php',
            '2025_01_17_035742_alter_nehemjlehs_table.php',
            
            // invoices related
            '2023_12_12_082329_add_column_invoices_table.php',
            '2023_12_12_082329_remove_column_invoices_table.php',
            '2023_12_27_082330_add_column_invoices_table.php',
            '2025_01_19_035742_alter_invoices_table.php',
            '2025_01_29_035742_alter_invoices_table.php',
            '2025_01_29_035744_alter_invoices_table.php',
            
            // erkhs related
            '2023_12_21_082329_remove_column_erkhs_table.php',
            '2023_12_21_082330_add_column_erkhs_table.php',
            '2025_01_29_035743_alter_erkhs_table.php',
            '2025_01_29_035745_alter_erkhs_table.php',
            '2025_02_03_035745_alter_erkhs_table.php',
            
            // device_dtls related
            '2025_01_19_035743_alter_device_dtls_table.php',
            
            // orc_tags related
            '2025_01_06_021558_alter_orc_tags_table.php',
            
            // package_months related
            '2024_01_24_082941_alter_package_months_table.php',
        ];
        
        $removedCount = 0;
        foreach ($migrationsToRemove as $migration) {
            $filePath = $this->originalMigrationsPath . '/' . $migration;
            if (file_exists($filePath)) {
                unlink($filePath);
                $removedCount++;
                echo "  ❌ Removed: {$migration}\n";
            }
        }
        
        echo "✅ Removed {$removedCount} original migrations\n\n";
    }
    
    private function copyConsolidatedMigrations()
    {
        echo "📋 Copying consolidated migrations...\n";
        
        $consolidatedFiles = glob($this->consolidatedMigrationsPath . '/*.php');
        $copiedCount = 0;
        
        foreach ($consolidatedFiles as $file) {
            $filename = basename($file);
            if ($filename !== 'consolidate_migrations.php') {
                $destPath = $this->originalMigrationsPath . '/' . $filename;
                copy($file, $destPath);
                $copiedCount++;
                echo "  ✅ Copied: {$filename}\n";
            }
        }
        
        echo "✅ Copied {$copiedCount} consolidated migrations\n\n";
    }
    
    private function verifyConsolidation()
    {
        echo "🔍 Verifying consolidation...\n";
        
        $files = glob($this->originalMigrationsPath . '/*.php');
        $migrationCount = count($files);
        
        echo "📊 Total migrations after consolidation: {$migrationCount}\n";
        
        // Check for any remaining ALTER migrations that might have been missed
        $alterMigrations = [];
        foreach ($files as $file) {
            $filename = basename($file);
            if (strpos($filename, 'alter_') !== false || strpos($filename, 'add_') !== false || strpos($filename, 'remove_') !== false) {
                $alterMigrations[] = $filename;
            }
        }
        
        if (!empty($alterMigrations)) {
            echo "⚠️  Warning: Found remaining ALTER migrations:\n";
            foreach ($alterMigrations as $migration) {
                echo "  - {$migration}\n";
            }
        } else {
            echo "✅ No remaining ALTER migrations found\n";
        }
        
        echo "\n";
    }
}

// Run the consolidation if this script is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $consolidator = new MigrationConsolidator();
    $consolidator->consolidate();
}
