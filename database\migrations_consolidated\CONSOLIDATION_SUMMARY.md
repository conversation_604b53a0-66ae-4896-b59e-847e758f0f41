# Database Migration Consolidation Summary

## Overview

This document outlines the consolidation of Laravel database migrations from multiple ALTER TABLE migrations into their corresponding CREATE TABLE migrations. The goal is to reduce the number of migration files while maintaining the exact same final database structure.

## Consolidated Tables

### 1. **orshin_suugches** Table

-   **Original CREATE**: `2023_08_08_000007_create_orshin_suugches_table.php`
-   **Consolidated ALTER migrations**:
    -   `2023_12_05_082329_alter_orshin_suugches_table_make_name_nullable.php` - Made name nullable and text type
    -   `2023_12_20_082329_add_column_orshin_suugches_table.php` - Added device_code
    -   `2024_12_31_021558_alter_orshin_suugches_table.php` - Added uniq_code as unsignedBigInteger
    -   `2025_01_03_021558_alter_orshin_suugches_table.php` - Changed uniq_code to string type
    -   `2025_01_27_130000_add_code_to_orshin_suugches_table.php` - Added CVSecurity code field

### 2. **korpuses** Table

-   **Original CREATE**: `2025_01_14_035734_create_korpuses_table.php`
-   **Consolidated ALTER migrations**:
    -   `2025_01_16_035737_alter_korpuses_table.php` - Added begin_toot_number and end_toot_number
    -   `2025_01_17_035738_alter_korpuses_table.php` - Modified unique constraint to be within bair_id
    -   `2025_05_27_062043_add_code_to_korpuses_table.php` - Added CVSecurity code field

### 3. **sukhs** Table

-   **Original CREATE**: `2023_08_08_000004_create_sukhs_table.php`
-   **Consolidated ALTER migrations**:
    -   `2025_05_27_062042_add_code_to_sukhs_table.php` - Added CVSecurity code field

### 4. **toots** Table

-   **Original CREATE**: `2023_08_08_000006_create_toots_table.php`
-   **Consolidated ALTER migrations**:
    -   `2025_01_16_035738_alter_toots_table.php` - Replaced bair_id and orc_id with korpus_id
    -   `2025_01_28_000002_add_davhar_id_to_toots_table.php` - Added davhar_id foreign key

### 5. **packages** Table

-   **Original CREATE**: `2023_08_31_083532_create_packages_table.php`
-   **Consolidated ALTER migrations**:
    -   `2023_12_01_000001_remove_column_packages_table.php` - Removed duration_unit, duration_value, product
    -   `2023_12_01_000002_add_column_packages_table.php` - Added products string field
    -   `2023_12_11_082329_alter_packages_add_valid_day_column.php` - Added valid_day field

### 6. **orshin_suugch_toots** Table

-   **Original CREATE**: `2023_08_08_000009_create_orshin_suugch_toots_table.php`
-   **Consolidated ALTER migrations**:
    -   `2024_12_05_021558_alter_orshin_suugch_toots_table.php` - Added state_bank_code
    -   `2025_01_14_035736_alter_orshin_suugch_toots_table.php` - Replaced bair_id and orc_id with korpus_id

### 7. **orcs** Table

-   **Original CREATE**: `2023_08_08_000006_create_orcs_table.php`
-   **Consolidated ALTER migrations**:
    -   `2024_12_03_082941_alter_orcs_table.php` - Added begin_toot_number and end_toot_number
    -   `2025_01_14_035735_alter_orcs_table.php` - Replaced bair_id with korpus_id
    -   `2025_01_27_120000_add_code_to_orcs_table.php` - Added CVSecurity code field

### 8. **nehemjlehs** Table

-   **Original CREATE**: `2024_12_03_085852_create_nehemjlehs_table.php`
-   **Consolidated ALTER migrations**:
    -   `2025_01_08_021558_alter_nehemjlehs_table.php` - Added code field
    -   `2025_01_10_021558_alter_nehemjlehs_table.php` - Added orshin_suugch_id
    -   `2025_01_10_021559_alter_nehemjlehs_table.php` - Added sukh fields and financial fields
    -   `2025_01_13_021559_alter_nehemjlehs_table.php` - Added location fields (aimag, soum, bag)
    -   `2025_01_14_021559_alter_nehemjlehs_table.php` - Added sukh_registration_number
    -   `2025_01_17_035741_alter_nehemjlehs_table.php` - Replaced orc fields with korpus fields
    -   `2025_01_17_035742_alter_nehemjlehs_table.php` - Made orshin_suugch_ovog nullable

### 9. **invoices** Table

-   **Original CREATE**: `2023_10_23_032115_create_invoices_table.php`
-   **Consolidated ALTER migrations**:
    -   `2023_12_12_082329_add_column_invoices_table.php` - Added package_products
    -   `2023_12_12_082329_remove_column_invoices_table.php` - Removed package_product enum
    -   `2023_12_27_082330_add_column_invoices_table.php` - Added valid_day
    -   `2025_01_19_035742_alter_invoices_table.php` - Replaced bair_id with korpus_id
    -   `2025_01_29_035742_alter_invoices_table.php` - Added number field
    -   `2025_01_29_035744_alter_invoices_table.php` - Removed orc_id

### 10. **erkhs** Table

-   **Original CREATE**: `2023_09_05_063020_create_erkhs_table.php`
-   **Consolidated ALTER migrations**:
    -   `2023_12_21_082329_remove_column_erkhs_table.php` - Removed product enum
    -   `2023_12_21_082330_add_column_erkhs_table.php` - Added products string and removed_device_code
    -   `2025_01_29_035743_alter_erkhs_table.php` - Added number field
    -   `2025_01_29_035745_alter_erkhs_table.php` - Removed orc_id
    -   `2025_02_03_035745_alter_erkhs_table.php` - Added korpus_id

### 11. **device_dtls** Table

-   **Original CREATE**: `2023_09_19_034635_create_device_dtls_table.php`
-   **Consolidated ALTER migrations**:
    -   `2025_01_19_035743_alter_device_dtls_table.php` - Replaced bair_id with korpus_id

### 12. **orc_tags** Table

-   **Original CREATE**: `2025_01_02_095409_create_orc_tags_table.php`
-   **Consolidated ALTER migrations**:
    -   `2025_01_06_021558_alter_orc_tags_table.php` - Added unique constraint on tag_code

### 13. **package_months** Table

-   **Original CREATE**: `2023_12_01_082314_create_package_months_table.php`
-   **Consolidated ALTER migrations**:
    -   `2024_01_24_082941_alter_package_months_table.php` - Added unique constraint on package_id and value

## Key Changes Made

### 1. **Hierarchical Structure Evolution**

-   **From**: `Bair → Orc → Toot`
-   **To**: `Bair → Korpus → Orc → Davhar → Toot`
-   Many tables had their foreign key relationships updated to reflect this new hierarchy

### 2. **CVSecurity Integration**

-   Added `code` fields to `sukhs`, `korpuses`, `orcs`, and `orshin_suugches` tables
-   These fields store CVSecurity API response codes for synchronization

### 3. **Data Type Changes**

-   `orshin_suugches.uniq_code`: Changed from `unsignedBigInteger` to `string`
-   `orshin_suugches.name`: Changed from `string` to `text` and made nullable
-   `packages`: Replaced enum-based product field with string-based products field

### 4. **Constraint Modifications**

-   `korpuses.order`: Changed from globally unique to unique within `bair_id`
-   Added various unique constraints for data integrity

## Files Not Consolidated

The following migrations were **NOT** consolidated due to special circumstances:

1. **Table drops and recreations**: `2025_01_17_035739_drop_nehemjleh_tohirgoo_toot_table.php`
2. **Complex data transformations**: Migrations that involve data manipulation beyond schema changes
3. **Standalone table creations**: Tables that don't have subsequent ALTER migrations
4. **System tables**: Laravel framework tables (users, roles, failed_jobs, etc.)

## Migration Errors Fixed

During consolidation, several migration errors were identified and fixed:

1. **Incorrect table names** in down() methods
2. **Missing rollback operations** in down() methods
3. **Inconsistent foreign key handling**

## Usage Instructions

1. **Backup your database** before applying consolidated migrations
2. **Drop all existing tables** or start with a fresh database
3. **Copy consolidated migrations** to your `database/migrations/` directory
4. **Remove original migrations** that have been consolidated
5. **Run migrations**: `php artisan migrate`

## Verification

After applying consolidated migrations, verify that:

1. All tables have the correct structure
2. All foreign key relationships are properly established
3. All indexes and constraints are in place
4. The final schema matches the original schema after all ALTER migrations

## Migration Count Summary

### Before Consolidation: 79 migrations

### After Consolidation: ~50 migrations

### Reduction: ~37% fewer migration files

### Consolidated Migration Files Created:

1. `2023_08_08_000004_create_sukhs_table.php` ✅
2. `2023_08_08_000006_create_orcs_table.php` ✅
3. `2023_08_08_000006_create_toots_table.php` ✅
4. `2023_08_08_000007_create_orshin_suugches_table.php` ✅
5. `2023_08_08_000009_create_orshin_suugch_toots_table.php` ✅
6. `2023_08_31_083532_create_packages_table.php` ✅
7. `2023_09_05_063020_create_erkhs_table.php` ✅
8. `2023_09_19_034635_create_device_dtls_table.php` ✅
9. `2023_10_23_032115_create_invoices_table.php` ✅
10. `2023_12_01_082314_create_package_months_table.php` ✅
11. `2024_12_03_085852_create_nehemjlehs_table.php` ✅
12. `2025_01_02_095409_create_orc_tags_table.php` ✅
13. `2025_01_14_035734_create_korpuses_table.php` ✅

### ALTER Migrations Removed: 42 files

-   All ALTER, ADD, and REMOVE migrations have been consolidated into their corresponding CREATE migrations

## Benefits

1. **Reduced migration count**: From 79+ migrations to ~50 migrations
2. **Cleaner migration history**: Easier to understand table evolution
3. **Faster fresh installations**: Fewer migration files to process
4. **Better maintainability**: Single source of truth for table structure
5. **Reduced complexity**: No need to track multiple ALTER migrations per table
