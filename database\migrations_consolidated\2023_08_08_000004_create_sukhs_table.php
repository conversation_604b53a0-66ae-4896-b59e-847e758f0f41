<?php

use App\Models\Sukh;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sukhs', function (Blueprint $table) {
            $table->id();
            $table->string(Sukh::NAME);
            $table->string(Sukh::REGISTRATION_NUMBER, 7)->unique();
            $table->string(Sukh::PHONE)->unique();
            $table->string(Sukh::EMAIL)->unique()->nullable();
            $table->foreignId(Sukh::AIMAG_ID)->nullable();
            $table->foreignId(Sukh::SOUM_ID)->nullable();
            $table->foreignId(Sukh::BAG_ID)->nullable();
            $table->string(Sukh::CODE, 30)->nullable(); // Added from 2025_05_27 (CVSecurity integration)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sukhs');
    }
};
