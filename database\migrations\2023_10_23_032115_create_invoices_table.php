<?php

use App\Models\Invoice;
use App\Models\Constant\ConstData;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId(Invoice::ORSHIN_SUUGCH_ID);
            $table->foreignId('korpus_id')->nullable()->constrained(); // Changed from bair_id
            $table->foreignId(Invoice::PACKAGE_ID);
            $table->string(Invoice::PACKAGE_NAME);
            $table->string('package_products')->nullable(); // Replaced package_product enum
            $table->string(Invoice::INVOICE_ID)->nullable();
            $table->string(Invoice::INVOICE_CODE)->nullable();
            $table->string(Invoice::SENDER_INVOICE_NO)->nullable();
            $table->string(Invoice::INVOICE_RECEIVER_CODE)->nullable();
            $table->string(Invoice::INVOICE_DESCRIPTION)->nullable();
            $table->decimal(Invoice::AMOUNT, 24, 2)->default(0)->nullable();
            $table->string(Invoice::STATUS)->default(ConstData::INVOICE_STATUS_PENDING);
            $table->integer('valid_day')->nullable();
            $table->unsignedBigInteger('number')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
