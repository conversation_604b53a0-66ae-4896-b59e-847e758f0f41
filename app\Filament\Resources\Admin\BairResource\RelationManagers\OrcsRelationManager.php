<?php

namespace App\Filament\Resources\Admin\BairResource\RelationManagers;

use App\Models\Orc;
use App\Models\Korpus;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class OrcsRelationManager extends RelationManager
{
    protected static string $relationship = 'orcs';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make(Orc::KORPUS_ID)
                            ->label('Блок')
                            ->options(function () {
                                $bairId = $this->getOwnerRecord()->id;
                                return Korpus::where('bair_id', $bairId)
                                    ->orderBy('order')
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->searchable(),
                        Forms\Components\TextInput::make(Orc::NUMBER)
                            ->label('Орцны дугаар')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $korpusId = $get('korpus_id');
                                    return $rule->where('korpus_id', $korpusId);
                                }
                            )
                            ->numeric()
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Orc::NUMBER)->label('Орцны дугаар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('davhars_count')->counts('davhars')->label('Давхар тоо'),
                Tables\Columns\TextColumn::make('toots_count')->counts('toots')->label('Тоот тоо'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        $bairId = $this->getOwnerRecord()->id;
        return parent::getTableQuery()
            ->whereHas('korpus', function ($query) use ($bairId) {
                $query->where('bair_id', $bairId);
            });
    }
}
