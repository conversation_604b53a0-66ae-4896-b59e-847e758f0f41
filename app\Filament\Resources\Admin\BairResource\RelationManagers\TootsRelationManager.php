<?php

namespace App\Filament\Resources\Admin\BairResource\RelationManagers;

use App\Models\Toot;
use App\Models\Davhar;
use App\Models\Orc;
use App\Models\Korpus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class TootsRelationManager extends RelationManager
{
    protected static string $relationship = 'toots';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('korpus_id')
                            ->label('Блок')
                            ->options(function () {
                                $bairId = $this->getOwnerRecord()->id;
                                return Korpus::where('bair_id', $bairId)
                                    ->orderBy('order')
                                    ->pluck('name', 'id');
                            })
                            ->live()
                            ->required()
                            ->searchable(),
                        Forms\Components\Select::make('orc_id')
                            ->label('Орц')
                            ->options(function (callable $get) {
                                $korpusId = $get('korpus_id');
                                if (!$korpusId) return [];
                                return Orc::where('korpus_id', $korpusId)
                                    ->orderBy('number')
                                    ->pluck('number', 'id');
                            })
                            ->live()
                            ->required()
                            ->searchable(),
                        Forms\Components\Select::make(Toot::DAVHAR_ID)
                            ->label('Давхар')
                            ->options(function (callable $get) {
                                $orcId = $get('orc_id');
                                if (!$orcId) return [];
                                return Davhar::where('orc_id', $orcId)
                                    ->orderBy('order')
                                    ->pluck('number', 'id');
                            })
                            ->required()
                            ->searchable(),
                        Forms\Components\TextInput::make(Toot::NUMBER)
                            ->label('Тоотын дугаар')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $davharId = $get('davhar_id');
                                    return $rule->where('davhar_id', $davharId);
                                }
                            )
                            ->numeric()
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('davhar.orc.number')->label('Орц')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('davhar.number')->label('Давхар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Toot::NUMBER)->label('Тоотын дугаар')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        $bairId = $this->getOwnerRecord()->id;
        return parent::getTableQuery()
            ->where('korpus_id', function ($query) use ($bairId) {
                $query->select('id')
                    ->from('korpuses')
                    ->where('bair_id', $bairId);
            });
    }
}
